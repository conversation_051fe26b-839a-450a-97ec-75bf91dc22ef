# Change Log
Les changements notables de ce projet seront documentés dans ce fichier.

## [2.0.3] - 2024-04-22
 
### Added

 
### Changed
 
### Fixed
- Changed the possibility to get the category name and family name "à classer" to "To be classified"

## [2.0.2] - 2024-04-08
 
### Added
- Started the ip rating table
- Ignored the E letter rating
 
### Changed
- Changed the duplicate data function
- Changed the patching cadence formula
 
### Fixed
- Added the IP level on rating
- Added the patching cadence info by assets

## [2.0.1] - 2024-02-26
 
### Added
- Added the environment name in mail
- Added the tag and commit name in logs
 
### Changed
- Changed the weights of patching cadence gain and lose
 
### Fixed
- Changed the variable ip_privacy for patching cadence table


## [2.0.0] - 2024-02-20
 
### Added
- Alimentation journalière en base des tables de rating par catégories, familles et globales.
- Création journalière des bases de données, d'assets, et de patching cadence.
- Envoi automatique d'un mail sur les notes générales du jour et des IP vulnérables.
- Création journalière des bases de données sur les corrections par catégories, familles et par IP.
- Récupération des notes n'ayant pas eu de rating depuis le délai d'analyse.
- Etude sur les IP publiques et privées.
- Variabilisation des informations suivantes:
  - `RATING_DURATION_COMPUTE`: Délai d'étude pour le rating
  - `THRESHOLD_VULNERABLE_IP_PENALTY`: Seuil des IP qualifiées de vulnérables pour le reporting par mail
  - `LIST_OF_SUBSIDIARY_ID_TO_REMOVE`: Liste des subsidiary à écarter de l'analyse
  - `THRESHOLD_HEALTHY_IP`: Seuil des IP qualifiées de saines
 
### Changed
 
### Fixed
