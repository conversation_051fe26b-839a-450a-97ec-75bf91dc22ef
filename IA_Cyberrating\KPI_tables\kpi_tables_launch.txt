######### Commandes à lancer pour obtenir les tables de KPI sur la machine IA #############

- La premiere ligne de commande permet de se placer dans le repertoire contenant les fonctions utiles pour obtenir les tables de distribution
- La deuxieme permet de se connecter en tant que user spark
- La troisieme permet d'acceder a l'interpreteur Python
- La quatrieme et cinquieme permettent a l'interpreteur de se placer dans le bon repertoire
- La sixieme permet d'importer les fonctions de distribution
- Les deux dernieres lignes permettent de lancer les fonctions et d'obtenir les indicateurs souhaites

cd /data/scripts
sudo su - spark
python3
import os
os.chdir("/data/scripts")
from kpi_tables import rating_kpi, findings_kpi
rating_kpi()
findings_kpi()