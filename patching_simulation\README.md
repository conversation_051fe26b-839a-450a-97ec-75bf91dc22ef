# Patching Simulation Dashboard

Cette application permet de simuler et visualiser la cadence de correction des vulnérabilités (patching cadence) au sein d’un parc informatique réparti par filiales. Elle est développée en **Dash (Plotly)** et permet d’ajuster dynamiquement les paramètres liés à la pénalisation et bonification du patching selon la criticité.

## Structure du projet

patching_simulation/
  ├── app.py # Application principale Dash
  ├── utils.py # Fonctions de calcul métier et statistiques
  ├── data_loader.py # Chargement et préparation des données
  ├── patching_history.csv # Données source de patching 
  ├── requirements.txt
  └── README.md

## Fonctionnalités

- **Simulation personnalisée** des notes de patching selon criticité (Low, Medium, High, Critical)
- **Histogrammes interactifs** des scores (par défaut vs personnalisés)
- **Tableau dynamique** par filiale avec filtres
- **Courbes d’évolution annuelles** des scores si correction de l'ensemble des vulnérabilités
- **Radars et graphiques en camembert** pour le détail des corrections par niveau de sévérité

##  Paramètres ajustables

Pour chaque niveau de criticité :
-  Gain (bonus en cas de correction rapide)
-  Lose (pénalité en cas de dépassement du temps de correction)
-  Factor max (limite du facteur multiplicateur de pénalité)

Ces paramètres influencent la note finale de chaque filiale.

##  Visualisations

- **Graphiques histogrammes** de répartition des scores
- **Diagramme circulaires** du statut de correction par criticité
- **Radar chart** des performances globales par filiale
- **Évolution temporelle** sur une année par filiale

##  Lancer l'application

### 1. Créer un environnement virtuel (recommandé)
```bash
python -m venv venv
source venv/bin/activate  # ou `venv\Scripts\activate` sous Windows
```
### 2. Installer les dépendances
```bash
pip install -r requirements.txt
```
### 3. Se placer dans le répertoir "patching_simulation/"
```bash
cd patching_simulation/
```
### 4. Prérequis !
Déposer un fichier csv nommé "patching_history" contenant l'historique des vulnérabilités.
(Ce fichier est un extract de la table: cyber_rating_patchingcadencenew")
### 4. Lancer le serveur Dash
```bash
python app.py
```
Puis ouvrir http://127.0.0.1:8050 dans votre navigateur.

