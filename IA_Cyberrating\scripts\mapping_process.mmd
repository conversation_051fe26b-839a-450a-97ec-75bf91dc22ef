flowchart TD
    A[Start: main.py]
    A --> B{Instantiating the ImportData class}
    B --> C[Loads configuration parameters from the database];
    C --> C1[-Get Tenable plugin data
             -Get historical patching cadence
             -Get rating tables
             -Get reference table
             -Get parameters]
    C1 --> C2[cleanning and assembling data]
    C2 --> D
    A --> D{Instantiating the TableConstruction class};
    D --> D1[Create category rating tables according to the privacy]
    D1 --> D2[Update of vulnerability history];
    D2 --> D3[Calculating Patching Cadence & creating Patching Cadence family];
    D3 --> D4[Create family rating tables according to the privacy];
    D4 --> D5[Concatenates public and private category/family/total rating]
    D5 --> D6[Attributes rating letters]
    D6 --> D7[Creating Data Finding]
    D3 --> D03[Calculates average correction times by severity]
    D7 --> D8[Sending 'data_today.csv' et 'vulnerable_ips.csv' by email]
    D03 --> D8
    A --> D8

    D8 --> E{Export rating tables}
    A --> E

    A --> F{Instantiating the Correction class}
    D7 --> F
    F --> F1[Create public and private category correction]
    F1 --> F2[Creates public and private family correction]
    F2 --> F3[Creates public and private ip correction]

    F3 --> H{Export the correction tables}
    A --> H
    
    H --> I
    E --> I
    A --> I{Check that the day's data is written into the tables}

    A --> G1{Instantiating the ADrating Class};
    G1 --> G2[Importing AD related data ad_audit, ad_domain_info, ...];
    G2 --> G3[Define setting weights];
    G3 --> G4[Filtering data:
              - Selection of results from last audit
              - Selection of settings with a critical rating other than 0];
    G4 --> G5[Calculation of penalties by settings];
    G5 --> G6[Calculation of rating by grouping];
    G6 --> G7[Calculation of rating by domain];
    G7 --> G8[Concatenation of results:
              - setting penalties
              - grouping rating
              - domain rating];
    G8 --> G9[Deletes today's AD rating data if exists];
    G9 --> G110[Exports AD rating data];
    