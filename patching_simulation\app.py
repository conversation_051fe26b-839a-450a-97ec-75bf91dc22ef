import dash
import dash_daq as daq
import numpy as np
from dash import dcc, html, Input, Output, State
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime

from utils import calculate_patching_cadence, stat_on_pc_df, prepar_tab, simulate_patching_cadence_over_year
from data_loader import load_and_prepare_data

# Initial data loading
pc_df_, subsidiaries = load_and_prepare_data()

app = dash.Dash(__name__)
server = app.server


# Paramètres par criticité (index : [low, medium, high, critical])
default_gain = [0, 1, 3, 5]
default_lose = [15, 135, 45, 30]
default_factor = [0, 4, 24, 73]

default_pc_df, default_df_ent = calculate_patching_cadence(pc_df_, default_gain, default_lose, default_factor, datetime.now())
default_pc_df_t, default_df_ent_t = calculate_patching_cadence(pc_df_, default_gain, default_lose, default_factor, datetime.now(), True)
default_res = stat_on_pc_df(default_pc_df, default_df_ent)
default_res = prepar_tab(default_res)

app = dash.Dash(__name__)
app.title = "PC Dashboard"

criticities = ["Low", "Medium", "High", "Critical"]


def make_input_group(label, id_, default):
    return html.Div([
        html.Label(label),
        dcc.Input(id=id_, type="number", value=default, step=1, debounce=True),
    ], style={'margin': '5px'})


# Layout
app.layout = html.Div([
    html.Div([
        html.Div([  # Flex container
            html.Div([
                html.H4(f"{crit}"),
                make_input_group("Gain", f"gain_{crit.lower()}", default_gain[i]),
                make_input_group("Lose", f"lose_{crit.lower()}", default_lose[i]),
                make_input_group("Factor Max", f"factor_{crit.lower()}", default_factor[i]),
            ], style={
                'padding': '10px',
                'border': '1px solid lightgrey',
                'border-radius': '10px',
                'background': '#f9f9f9',
                'margin-right': '20px',
                'width': '250px',
                'box-shadow': '2px 2px 8px rgba(0,0,0,0.05)'
            })
            for i, crit in enumerate(criticities)
        ], style={
            'display': 'flex',
            'flex-direction': 'row',
            'justify-content': 'space-around',
            'flex-wrap': 'wrap'  # au cas où l'écran est petit
        })
    ]),

    html.Button("Mettre à jour les graphiques", id="submit-btn", n_clicks=0),

    html.Hr(),

    # Onglets
    dcc.Tabs([
        dcc.Tab(label="Répartition des notes par filiale", children=[
            html.Div([
                html.H2("Histogramme des notes de patching"),
                html.Div([
                    daq.ToggleSwitch(
                        id="show-default",
                        label="Afficher valeurs par défaut",
                        value=False
                    )
                ]),
                dcc.Graph(id="pc_histogram"),
                dcc.Graph(id="pc_histogram_real_value")
            ])
        ]),

        dcc.Tab(label="Tableau dynamique", children=[
            html.Div([
                html.H2("Tableau par filiale"),
                # Dropdown pour filtrer par filiale
                dcc.Dropdown(
                    id="subsidiary-filter",
                    options=[{"label": name, "value": id} for id, name in zip(default_res['subsidiary'], default_res['subsidiary_name'])],
                    value=default_res['subsidiary'].tolist(),  # Valeur initiale, toutes les filiales
                    multi=True,
                    placeholder="Sélectionner une ou plusieurs filiales"
                ),
                dcc.Graph(id="pc_table")  # Graphique pour afficher le tableau
            ])
        ]),

        dcc.Tab(label="Évolution des notes sur 1 an", children=[
            html.Div([
                html.H2("Évolution annuelle de la note par filiale"),
                dcc.Dropdown(
                    id="subsidiary-filter_2",
                    options=[{"label": name, "value": id} for id, name in zip(default_res['subsidiary'], default_res['subsidiary_name'])],
                    placeholder="Choisir une filiale",
                    multi=False  # Permet de sélectionner une seule filiale
                ),
                dcc.Graph(id="pc_curve"),
                dcc.Graph(id="radar_chart"),
                html.Div([
                    dcc.Graph(id=f"pie_{sev}") for sev in ['low', 'medium', 'high', 'critical']
                ], style={"display": "flex", "flexWrap": "wrap", "justifyContent": "space-between"}),

            ])
        ])
    ])
])


def get_histogram_bar_trace(series, label, color, bin_size=10, range_min=0, range_max=100):
    # Calcul des bins
    bins = np.arange(range_min, range_max + bin_size, bin_size)
    counts, edges = np.histogram(series, bins=bins)

    # Milieu des bins pour les labels X
    bin_centers = 0.5 * (edges[:-1] + edges[1:])

    return go.Bar(
        x=bin_centers,
        y=counts,
        text=counts,
        textposition="inside",
        name=label,
        marker_color=color
    )


@app.callback(
    [Output("pc_histogram", "figure"),
     Output("pc_histogram_real_value", "figure")],
    [Input("submit-btn", "n_clicks"),
     Input("show-default", "value")],
    [State(f"gain_{c.lower()}", "value") for c in criticities] +
    [State(f"lose_{c.lower()}", "value") for c in criticities] +
    [State(f"factor_{c.lower()}", "value") for c in criticities],
)
def update_graph(n_clicks, show_default, *params):
    # Séparer les paramètres
    gain_values = list(params[0:4])
    lose_values = list(params[4:8])
    factor_values = list(params[8:12])

    # Calcul des résultats avec les paramètres custom
    df_copy = pc_df_.copy()
    df, df_ent = calculate_patching_cadence(df_copy, gain_values, lose_values, factor_values, datetime.now(), True)
    _, df_ent_real = calculate_patching_cadence(df_copy, gain_values, lose_values, factor_values, datetime.now())

    fig = go.Figure()

    fig.add_trace(get_histogram_bar_trace(
        df_ent['family_rating'], "Paramètres personnalisés", "blue"
    ))

    if show_default:
        fig.add_trace(get_histogram_bar_trace(
            default_df_ent_t['family_rating'], "Valeurs par défaut", "orange"
        ))

    fig.update_layout(
        title="Distribution des notes",
        xaxis_title="Note",
        yaxis_title="Nombre d'occurrences",
        barmode="group",
        bargap=0.2
    )

    fig.update_layout(
        title="Distribution des notes",
        xaxis_title="Note",
        yaxis_title="Count",
        barmode="group",
        bargap=0.2
    )
    all_ratings = df_ent_real["family_rating"]
    if show_default:
        all_ratings = pd.concat([all_ratings, default_df_ent["family_rating"]])

    # Calculer min/max pour définir le range des bins
    range_min = int(np.floor(all_ratings.min() / 10) * 10)

    fig_2 = go.Figure()

    fig_2.add_trace(get_histogram_bar_trace(
        df_ent_real["family_rating"], "Paramètres personnalisés", "blue", 10,
        range_min
    ))

    if show_default:

        fig_2.add_trace(get_histogram_bar_trace(
            default_df_ent["family_rating"], "Valeurs par défaut", "orange", 10,
            range_min
        ))

    fig_2.update_layout(
        title="Distribution des notes réelles",
        xaxis_title="Note",
        yaxis_title="Nombre d'occurrences",
        barmode="group",
        bargap=0.2
    )

    return [fig, fig_2]


# Callback pour mettre à jour le tableau dynamique en fonction du filtre par filiale
@app.callback(
    Output("pc_table", "figure"),
    [Input("subsidiary-filter", "value"),
     Input("submit-btn", "n_clicks")],
    [State(f"gain_{c.lower()}", "value") for c in criticities] +
    [State(f"lose_{c.lower()}", "value") for c in criticities] +
    [State(f"factor_{c.lower()}", "value") for c in criticities],
)
def update_table(selected_subs, n_clicks, *params):
    gain_values = list(params[0:4])
    lose_values = list(params[4:8])
    factor_values = list(params[8:12])
    # Filtrer les données en fonction des filiales sélectionnées
    pc_df, df_ent = calculate_patching_cadence(pc_df_, gain_values, lose_values, factor_values, datetime.now())
    res = stat_on_pc_df(pc_df, df_ent)
    res = prepar_tab(res)
    filtered_df = res[res['subsidiary'].isin(selected_subs)]

    # Créer le tableau interactif avec Plotly
    fig = go.Figure(data=[go.Table(
        header=dict(values=["ID Filiale", "Nom Filiale", "pc_rating", "Proportion Medium corrigé dans les délais", 
                            "Proportion High corrigé dans les délais", "Proportion Critical corrigé dans les délais",
                            "Temps moyen Medium hors délais", "Temps moyen High hors délais", "Temps moyen Critical hors délais"]),
        cells=dict(values=[filtered_df['subsidiary'], filtered_df['subsidiary_name'], filtered_df['family_rating'],
                           filtered_df['prop_medium'], filtered_df['prop_high'], filtered_df['prop_critical'],
                           filtered_df['cor_mean_medium'], filtered_df['cor_mean_high'], filtered_df['cor_mean_critical']])
    )])

    fig.update_layout(title="Tableau des proportions et temps moyens de correction")
    return fig


# Callback pour le graphique d'évolution des notes sur 1 an et le radar chart
@app.callback(
    [Output("pc_curve", "figure"),
     Output("radar_chart", "figure"),
     Output("pie_low", "figure"),
     Output("pie_medium", "figure"),
     Output("pie_high", "figure"),
     Output("pie_critical", "figure")],
    [Input("subsidiary-filter_2", "value"),
     Input("submit-btn", "n_clicks")],
    [State(f"gain_{c.lower()}", "value") for c in criticities] +
    [State(f"lose_{c.lower()}", "value") for c in criticities] +
    [State(f"factor_{c.lower()}", "value") for c in criticities],
)
def update_graphs(selected_subs, n_clicks, *params):
    if selected_subs is None:
        return {}, {}
    gain_values = list(params[0:4])
    lose_values = list(params[4:8])
    factor_values = list(params[8:12])

    # --- 1. Préparer les données pour la courbe d'évolution des notes
    data = simulate_patching_cadence_over_year(pc_df_, gain_values, lose_values, factor_values, [selected_subs])

    # Courbe d'évolution des notes (évolution de la 'family_rating' sur l'année)
    fig_curve = go.Figure()
    fig_curve.add_trace(go.Scatter(
        x=data['timestamp'],
        y=data['family_rating'],
        mode='lines+markers',
        name="Évolution des notes",
        line=dict(color='teal')
    ))

    fig_curve.update_layout(
        title="Évolution des notes de patching sur 1 an",
        xaxis_title="Date",
        yaxis_title="Family Rating",
        template="plotly_dark"
    )

    # --- 2. Préparer les données pour le radar chart
    # Récupérer les informations de la filiale sélectionnée
    pc_df, df_ent = calculate_patching_cadence(pc_df_, gain_values, lose_values, factor_values, datetime.now())
    res = stat_on_pc_df(pc_df, df_ent)
    res = prepar_tab(res)

    row = res[res['subsidiary'] == selected_subs]
    rating = row['family_rating'].values[0]  # On suppose qu'il y a une seule ligne pour chaque filiale
    categories = ['Low', 'Medium', 'High', 'Critical', 'Family Rating']
    values = row[['prop_low', 'prop_medium', 'prop_high', 'prop_critical']].values.flatten().tolist()

    # Ajouter la note de famille au graphique radar
    normalized_rating = rating  # Normalisation de la note si nécessaire
    values.append(normalized_rating)

    # Fermer le cercle du radar
    values += values[:1]
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]

    # Radar Chart
    radar_fig = go.Figure(data=[go.Scatterpolar(
        r=values,
        theta=categories,
        fill='toself',
        line=dict(color='teal', width=2),
        name="Correction & Note"
    )])

    radar_fig.update_layout(
        title=f"Radar - Proportion de correction par sévérité & Note",
        polar=dict(
            radialaxis=dict(range=[0, 100])
        ),
        template="plotly_dark"
    )

    severity_map = {
        'low': 1,
        'medium': 2,
        'high': 3,
        'critical': 4
        }
    pie_charts = []

    # Filtrer sur la filiale sélectionnée
    sub_df = pc_df[pc_df['subsidiary'] == selected_subs]

    for severity in severity_map.keys():
        sev_df = sub_df[sub_df["severity"] == severity_map[severity]]

        labels = []
        values = []
        customdata = []
        colors = []
        global_delay_mean = sev_df["correction_delay"].mean()
        global_penalty_mean = sev_df["penalty"].mean()
        # Corrigé
        corrected = sev_df[sev_df["correction"] == True]
        if not corrected.empty:
            # Corrigé dans les temps
            in_time = corrected[corrected["penalty"] >= 0]
            labels.append("Corrigé dans les temps")
            values.append(len(in_time))
            delay_mean = in_time["correction_delay"].mean()
            penalty_mean = in_time["penalty"].mean()
            customdata.append([f"{delay_mean:.0f}j", f"{penalty_mean:.2f}"])
            colors.append("green")

            # Corrigé hors délais
            late = corrected[corrected["penalty"] < 0]
            labels.append("Corrigé hors délais")
            values.append(len(late))
            delay_mean = late["correction_delay"].mean()
            penalty_mean = late["penalty"].mean()
            customdata.append([f"{delay_mean:.0f}j", f"{penalty_mean:.2f}"])
            colors.append("orange")

        # Non corrigé
        not_corrected = sev_df[sev_df["correction"] == False]
        if not not_corrected.empty:
            # Pas encore hors délais
            pending = not_corrected[not_corrected["penalty"] >= 0]
            labels.append("Non corrigé - délai en cours")
            values.append(len(pending))
            delay_mean = pending["correction_delay"].mean()
            customdata.append([f"{delay_mean:.0f}j", "0.00"])
            colors.append("yellow")

            # Hors délais
            overdue = not_corrected[not_corrected["penalty"] < 0]
            labels.append("Non corrigé - hors délais")
            values.append(len(overdue))
            delay_mean = overdue["correction_delay"].mean()
            penalty_mean = overdue["penalty"].mean()
            customdata.append([f"{delay_mean:.0f}j", f"{penalty_mean:.2f}"])
            colors.append("red")

        fig = go.Figure(data=[go.Pie(
            labels=labels,
            values=values,
            marker=dict(colors=colors),
            customdata=customdata,
            hovertemplate=("""
                %{label}<br>
                Nombre: %{value}<br>
                Délai moyen: %{customdata[0][0]}<br>
                Pénalité moyenne: %{customdata[0][1]}<br>
                <extra></extra>"""
            ),
            textinfo="label+value+percent"
        )])

        fig.update_layout(
            title=(
                f"Répartition correction - {severity.capitalize()}<br>"
                f"Délai moyen : {global_delay_mean:.0f}j | "
                f"Pénalité moyenne : {global_penalty_mean:.2f}"
            ),
            margin=dict(t=60, b=20)
        )
        pie_charts.append(fig)
    return [fig_curve, radar_fig] + pie_charts


if __name__ == '__main__':
    app.run(debug=False)
