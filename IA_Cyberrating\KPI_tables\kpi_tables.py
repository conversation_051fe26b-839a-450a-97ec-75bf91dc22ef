import pandas as pd
from src.db_connexion import db_connexion
import datetime

engine = db_connexion()

# Data importation
db_total = pd.read_sql("SELECT * FROM cyber_rating_orangecyberratinggrade", engine)
db_family = pd.read_sql("SELECT * FROM cyber_rating_orangecyberratinggradefamily", engine)
db_category = pd.read_sql("SELECT * FROM cyber_rating_orangecyberratinggradecategory", engine)
rating_letters = pd.read_sql("SELECT * FROM cyber_rating_ratinglettersetting ORDER BY value DESC", engine)
ref_subsidiary = pd.read_sql("SELECT * FROM organization_subsidiary", engine)
ref_cluster = pd.read_sql("SELECT * FROM organization_cluster", engine)
ref_division = pd.read_sql("SELECT * FROM organization_division", engine)
ref_asset = pd.read_sql('SELECT * FROM inventory_criticalasset', engine)


############# RATING DISTRIBUTION ###################

def rating_kpi(db_total = db_total, db_family = db_family, db_category = db_category, rating_letters = rating_letters, level = 'subsidiary', family = None, category = None) :
    """
    db_total : table containing total ratings,
    db_family : table containing family ratings,
    db_category : table containing category ratings,
    rating_letters : table containing rating letters,
    level : default subsidiary, can be subsidiary, asset, cluster, division or group based calculation
    family : default None, can be one of the ocr families in base,
    category : default None, can be one of the ocr categories in base

    return a table with :
    - total_rating_range : [90,100], [80,90], [70,80], [60,70], [50,60], [0,50],
    - counter : the number of entities in this range of rate,
    - prop_cumul : the cumulative proportion of entities in the above ranges
    """

    # exception notifiant que si l'utilisateur renseigne en même temps une famille et une catégorie, le calcul ne s'effectue pas
    if ((family is not None) & (category is not None)) :
        return("family and category parameters can't be activated at the same time.")
    else :
        # on récupère la lettre associée à la note et on l'associe à la variable range
        rating_ranges = []
        for i in range(len(rating_letters.sort_values(by='value', ascending = False))) :
            if i == 0 :
                rating_ranges.append('['+str(rating_letters.loc[i,'value'])+',100]')
            elif i < len(rating_letters)-1 :
                rating_ranges.append('['+str(rating_letters.loc[i,'value'])+','+str(rating_letters.loc[i-1,'value'])+'[')
            else :
                rating_ranges.append('<'+str(str(rating_letters.loc[i,'value'])))
        rating_letters['range'] = rating_ranges

        # on récupère les notes qui nous intéresse (si la variable family est renseignée : notes de la famille, si la variable catégorie est renseignée : notes de la catégorie, sinon : note globale)
        if family is not None :
            kpi_rating = db_family[(db_family.level == level) & (db_family.timestamp == max(db_family.timestamp)) & (db_family.orange_cyber_rating_family_id == family)]
        elif category is not None :
            kpi_rating = db_category[(db_category.level == level) & (db_category.timestamp == max(db_category.timestamp)) & (db_category.orange_cyber_rating_category_id == category)]
        else :
            kpi_rating = db_total[(db_total.level == level) & (db_total.timestamp == max(db_total.timestamp))]
            
        kpi_rating = kpi_rating.merge(rating_letters, left_on = 'rating_letter_id', right_on = 'id')

        # on récupère les indicateurs souhaitée : le nombre d'entités dans chaque intervalle de note
        kpi_rating = kpi_rating[['range','letter']].value_counts().sort_index(ascending = False).reset_index()
        kpi_rating.columns = ['total_rating_range','letter','counter']

        # on calcule la proportion cumulée d'entités avec au moins la note de l'intervalle
        kpi_rating['prop'] = [round(kpi_rating.loc[i,'counter']/sum(kpi_rating.counter),2) for i in range(len(kpi_rating))]
        kpi_rating['counter'] = [str(i)+'/'+str(sum(kpi_rating.counter)) for i in kpi_rating.counter]

        return kpi_rating

kpi_rating = rating_kpi()

#####################################################


############ FINDINGS COUNTER DISTRIBUTION ##########

def findings_kpi(db_total = db_total, db_family = db_family, db_category = db_category, level = 'subsidiary', family = None, category = None) :
    """
    db_total : table containing total ratings,
    db_family : table containing family ratings,
    db_category : table containing category ratings,
    level : default subsidiary, can be subsidiary, asset, cluster, division or group based calculation
    family : default None, can be one of the ocr families in base,
    category : default None, can be one of the ocr categories in base

    return a table with :
    - total_findings_range : > 10000, > 1000, > 100, < 100 findings,
    - counter : the number of entities in this range of findings,
    - prop_cumul : the cumulative proportion of entities in the above ranges
    """
    # exception notifiant que si l'utilisateur renseigne en même temps une famille et une catégorie, le calcul ne s'effectue pas
    if ((family is not None) & (category is not None)) :
        return("family and category parameters can't be activated at the same time.")
    else :
        # on récupère les données qui nous intéresse (si le paramètre family est renseigné : findings_counter de la famille, si le paramètre catégorie est renseigné : findings_counter de la catégorie, sinon : findings_counter global)
        if family is not None :
            kpi_counter = db_family[(db_family.level == level) & (db_family.timestamp == max(db_family.timestamp)) & (db_family.orange_cyber_rating_family_id == family)]
        elif category is not None :
            kpi_counter = db_category[(db_category.level == level) & (db_category.timestamp == max(db_category.timestamp)) & (db_category.orange_cyber_rating_category_id == category)]
        else :
            kpi_counter = db_total[(db_total.level == level) & (db_total.timestamp == max(db_total.timestamp))]
        
        # on récupère l'intervalle d'appartenance de chaque valeur de findings_counter
        cnt_range = [10000 if i > 10000 else 1000 if i >= 1000 else 100 if i >= 100 else 0 for i in kpi_counter.findings_counter]
        kpi_counter = kpi_counter.assign(counter_range = cnt_range)

        # on sort le nombre d'entités dans chaque intervalle de valeurs
        kpi_counter = kpi_counter[['counter_range']].value_counts().sort_index(ascending = False).reset_index()
        kpi_counter.columns = ['total_findings_range','counter']

        kpi_counter['total_findings_range'] = ['> 10000' if i == 10000 else '[1000,10000]' if i == 1000 else '[100,1000[' if i == 100 else '< 100' for i in kpi_counter.total_findings_range]

        # on calcule la proportion cumulée d'entités avec au moins le nombre de findings_counter de l'intervalle        
        kpi_counter['prop'] = [round(kpi_counter.loc[i,'counter']/sum(kpi_counter.counter),2) for i in range(len(kpi_counter))]
        kpi_counter['counter'] = [str(i)+'/'+str(sum(kpi_counter.counter)) for i in kpi_counter.counter]

        return kpi_counter

kpi_findings = findings_kpi()

#################################################


############ NEW ENTITIES ##########

def new_entities(level = 'subsidiary', db_total = db_total, ref_subsidiary = ref_subsidiary, ref_cluster = ref_cluster, ref_division = ref_division, ref_asset = ref_asset) :
    """
    This function returns the entities available in today calculation, in comparison with yesterday.

    level : default subsidiary, can be subsidiary, asset, cluster, division or group based calculation

    returns a table with :
    - subsidiary_id,
    - long_name,
    - total_rating,
    - findings_counter.
    """
    today = datetime.date.today()
    yesterday = datetime.date.today() - datetime.timedelta(days=1)

    db_total_today = db_total[(db_total.timestamp == today) & (db_total.level == level)]
    db_total_yesterday = db_total[(db_total.timestamp == yesterday) & (db_total.level == level)]
    print(db_total_today)

    if level == 'subsidiary' :
        ref = ref_subsidiary
        level_id = 'subsidiary_id'
    elif level == 'cluster' :
        ref = ref_cluster
        ref = ref.assign(long_name = ref['name'])
        level_id = 'cluster_id'
    elif level == 'division' :
        ref = ref_division
        level_id = 'division_id'
    elif level == 'asset' :
        ref = ref_asset
        ref = ref.assign(long_name = ref['name'])
        level_id = 'asset_id'
    print(ref)
    print(level_id)

    sub_today = db_total_today[level_id].unique().tolist()
    sub_yesterday = db_total_yesterday[level_id].unique().tolist()

    new_sub = [i for i in sub_today if i not in sub_yesterday]

    new_entities = db_total_today[db_total_today[level_id].isin(new_sub)]
    new_entities = new_entities[[level_id,'total_rating','findings_counter']].merge(ref[['id','long_name']], left_on = level_id, right_on = 'id')
    
    new_entities = new_entities[[level_id,'long_name','total_rating','findings_counter']]

    return new_entities

new = new_entities()

#################################################