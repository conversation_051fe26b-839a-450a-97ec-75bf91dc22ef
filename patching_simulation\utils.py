import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
from datetime import datetime, timedelta


def assign_pc_weight(corrected, cor_del, pc_days, severity, max_scan_date,
                     pc_penalty_lose, pc_penalty_gain, pc_factor_max, date_now):
    penalty = 0
    if corrected is False and cor_del <= pc_days:
        penalty = 0
    elif corrected and cor_del <= pc_days:
        penalty = pc_penalty_gain[severity - 1]
    elif corrected and cor_del > pc_days:
        delta_months = (date_now.year - max_scan_date.year) * 12 + date_now.month - max_scan_date.month
        coef_deg = max(0, 1 - (delta_months // 3) * 0.25)
        penalty = -min((cor_del // pc_days), pc_factor_max[severity - 1]) * pc_penalty_lose[severity - 1] * coef_deg
    elif corrected is False and cor_del > pc_days:
        penalty = -min((cor_del // pc_days), pc_factor_max[severity - 1]) * pc_penalty_lose[severity - 1]
    return penalty


def sum_within_range(x, real_value):
    if real_value:
        sum_result = min(max(100 + x.mean(), 0), 100)
    else:
        sum_result = min(100 + x.mean(), 100)
    return sum_result


def calculate_patching_cadence(pc_df, gain_values, lose_values, factor_values, date_now=datetime.now(), real_value=False):
    delta_year = date_now - timedelta(days=365)
    pc_df = pc_df[pc_df["subsidiary"].notna()]
    entities_list = ["division", "cluster", "subsidiary"]
    pc_df.loc[:, "max_scan_date"] = pd.to_datetime(pc_df["max_scan_date"])
    # Sous-ensemble où correction == False
    pc_df.loc[:, "correction"] = pc_df["correction"].astype(bool)
    df_correction_false = pc_df[~pc_df["correction"]]
    # Sous-ensemble où correction == True et max_scan_date >= delta_year
    df_correction_true = pc_df[(pc_df["correction"])]
    df_correction_true = df_correction_true[(df_correction_true["max_scan_date"] >= delta_year)]

    # Concaténer les deux sous-ensembles
    pc_df = pd.concat([df_correction_false, df_correction_true])

    pc_df.loc[:, "penalty"] = pc_df.apply(
        lambda row: assign_pc_weight(
            row["correction"],
            row["correction_delay"],
            row["patching_cadence_days"],
            row["severity"],
            row["max_scan_date"],
            pc_penalty_lose=lose_values,
            pc_penalty_gain=gain_values,
            pc_factor_max=factor_values,
            date_now=date_now
        ),
        axis=1)

    list_column = entities_list + ["penalty", "orange_cyber_rating_family_id",
                                    "orange_cyber_rating_category_id"]
    df = pc_df[list_column].groupby(entities_list, as_index=False).apply(lambda x: pd.Series(
        {"family_rating": sum_within_range(x["penalty"], real_value=real_value)}))
    df["timestamp"] = date_now
    return pc_df, df


def stat_on_pc_df(pc_df, df):
    prop = pc_df.groupby(['subsidiary', 'subsidiary_name', "severity"]).apply(
        lambda x: (x['penalty'] >= 0).mean()*100).reset_index(
            name='proportion_correction_delay_zero')
    prop_pivot = prop.pivot_table(index=['subsidiary', 'subsidiary_name'], columns='severity',
                                  values='proportion_correction_delay_zero',
                                  fill_value=100)

    prop_pivot = prop_pivot.reset_index()
    # Renommer les colonnes pour plus de clarté (optionnel)
    prop_pivot.columns.name = None
    prop_pivot = prop_pivot.rename(columns={
        1: 'prop_low',
        2: 'prop_medium',
        3: 'prop_high',
        4: 'prop_critical'
    })

    tps_cor_hd = pc_df[pc_df["penalty"] < 0]
    tps_cor_hd = tps_cor_hd.groupby(['subsidiary', "severity"]).apply(
        lambda x: (x['correction_delay']).mean()).reset_index(
            name='correction_delay_mean')
    tps_cor_hd_pivot = tps_cor_hd.pivot_table(index='subsidiary', columns='severity',
                                              values='correction_delay_mean',
                                              fill_value=0)
    tps_cor_hd_pivot = tps_cor_hd_pivot.reset_index()
    # Renommer les colonnes pour plus de clarté (optionnel)
    tps_cor_hd_pivot.columns.name = None
    tps_cor_hd_pivot = tps_cor_hd_pivot.rename(columns={
        1: 'cor_mean_low',
        2: 'cor_mean_medium',
        3: 'cor_mean_high',
        4: 'cor_mean_critical'
    })
    res = df.merge(prop_pivot, on="subsidiary", how="left")
    res = res.merge(tps_cor_hd_pivot, on="subsidiary", how="left")
    return res


def simulate_patching_cadence_over_year(pc_df, gain_values, lose_values, factor_values, sub_id=None):
    # Forcer toutes les corrections à True
    if sub_id:
        pc_df = pc_df[pc_df["subsidiary"].isin(sub_id)]
    pc_df.loc[:, "correction"] = True

    # Convertir les dates
    pc_df.loc[:, "max_scan_date"] = pd.to_datetime(pc_df["max_scan_date"])

    results = []

    for i in range(366):  # Inclure le jour 0
        current_day = datetime.now() - timedelta(days=25) + timedelta(days=i)
        # Calcul du patching cadence pour ce jour
        _, df_day = calculate_patching_cadence(pc_df, gain_values, lose_values, factor_values, current_day)
        results.append(df_day)

    # Concaténation des résultats journaliers
    final_df = pd.concat(results, ignore_index=True)
    return final_df


def prepar_tab(res):
    float_cols = ['family_rating', 'prop_medium', 'prop_high', 'prop_critical', 
                  'cor_mean_medium', 'cor_mean_high', 'cor_mean_critical']
    res[float_cols] = res[float_cols].round(0)
    prop_cols = ['prop_medium', 'prop_high', 'prop_critical']
    for col in prop_cols:
        res[col] = res[col].round(0).astype(int).astype(str) + '%'
    return res


# def get_parc_size(self, level, df):
#     if level == "asset":
#         ref_ip = self.ref_ip_asset
#     else:
#         ref_ip = self.ref_ip
#     ref = ref_ip[(~ref_ip["last_scanned_at"].isnull())]

#     if level == "group":
#         df["parc_size"] = len(ref)
#     elif level != "cluster":
#         df_parc = ref.groupby([level]).size().reset_index()
#         df_parc.rename(columns={0: "parc_size"}, inplace=True)

#         df = df.merge(df_parc[[level, "parc_size"]],
#                         on=[level],
#                         how="left")
#     else:
#         fake_cluster = df[level] > 1000
#         true_cluster = ~fake_cluster

#         df_parc_sub = ref.groupby(["subsidiary"]).size().reset_index()
#         df_parc_sub.rename(columns={0: "parc_size"}, inplace=True)
#         df_parc_sub["subsidiary"] = df_parc_sub["subsidiary"] + 1000

#         df_fake = df[fake_cluster].merge(
#             df_parc_sub[["subsidiary", "parc_size"]],
#             left_on=level, right_on="subsidiary",
#             how="left")

#         df_parc_clust = ref.groupby([level]).size().reset_index()
#         df_parc_clust.rename(columns={0: "parc_size"}, inplace=True)

#         df_true = df[true_cluster].merge(
#             df_parc_clust[[level, "parc_size"]],
#             on=level,
#             how="left")

#         df = pd.concat([df_true, df_fake], axis=0)
#     df.fillna({"parc_size": 0}, inplace=True)
#     return df


# def get_issued_ips(df, level, plugin_level, data_ip):
#     if level == "group":
#         df["issued_ips"] = data_ip["ip_id"].nunique()
#     else:
#         if plugin_level != "total":
#             df_issued_ips = data_ip.groupby([level, plugin_level]).size().reset_index()
#             df_issued_ips.rename(columns={0: "issued_ips"}, inplace=True)
#         else:
#             df_issued_ips = data_ip.groupby([level])["ip_id"].nunique().reset_index()
#             df_issued_ips.rename(columns={"ip_id": "issued_ips"}, inplace=True)

#         if plugin_level != "total":
#             df = df.merge(df_issued_ips[[level, plugin_level, "issued_ips"]],
#                             on=[level, plugin_level],
#                             how="left")
#         else:
#             df = df.merge(df_issued_ips[[level, "issued_ips"]],
#                             on=[level],
#                             how="left")
#     df.fillna({"issued_ips": 0}, inplace=True)
#     return df


# def calculate_up_entity(self, level, plugin_level, df):
#     if plugin_level == "family":
#         plugin_list = [plugin_level]
#     elif plugin_level == "category":
#         plugin_list = [plugin_level, "family"]
#     else:
#         plugin_list = []

#     list_all_levels = ["group"] + self.list_level
#     list_gb = list_all_levels[:list_all_levels.index(level) + 1] + plugin_list
#     list_column = list_gb + [plugin_level + "_rating", "parc_size"]
#     if not df.empty:
#         df = df[~df["remove_" + list_all_levels[list_all_levels.index(level) + 1]]]
#         df = df[list_column].groupby(list_gb).apply(lambda x: pd.Series(
#             {plugin_level + "_rating": self.log_wa(x[plugin_level + "_rating"], x["parc_size"]),
#                 "parc_size_sum": (x["parc_size"] + 1).apply(math.log).sum()}))
#         if not df.empty:
#             df = df.reset_index()
#         df = self.get_parc_size(level, df)
#         df = self.get_issued_ips(df, level, plugin_level, self.data_ip)
#         # df = self.remove_entity(df, level)
#         df["ip_privacy"] = self.privacy
#         df["level"] = level
#         df["asset"] = np.nan
#         for sub_level in list_all_levels[list_all_levels.index(level) + 1:]:
#             df[sub_level] = np.nan
#     else:
#         df["parc_size_sum"] = None
#     return df


# def join_all_level(self, plugin_level, df_sub, df_clust, df_div, df_group, df_asset):
#     df_list = [df_sub, df_clust, df_div, df_group, df_asset]
#     # Identifier l'ensemble des colonnes présentes dans les DataFrames
#     all_columns = pd.Index([])
#     for df in df_list:
#         all_columns = all_columns.union(df.columns)
#     df_non_empty = [df for df in df_list if not df.isna().all().all()]
#     if df_non_empty:
#         df = pd.concat(df_non_empty, ignore_index=True)
#         df = df.reindex(columns=all_columns)
#         df.insert(0, "timestamp", date.today())
#     else:
#         df = pd.DataFrame()
#         df = df.reindex(columns=all_columns)

#     if plugin_level == "family":
#         plugin_list = [plugin_level, plugin_level + "_rating"]
#     elif plugin_level == "category":
#         plugin_list = ["family", plugin_level, plugin_level + "_rating"]
#     else:
#         plugin_list = ["total_rating"]

#     try:
#         df = df[["timestamp", "ip_privacy", "level", "group"] +
#                 self.list_level + ["asset"] +
#                 plugin_list + ["parc_size", "issued_ips", "parc_size_sum"]]
#     except KeyError:
#         df = pd.DataFrame(columns=["timestamp", "ip_privacy", "level", "group"] +
#                             self.list_level + ["asset"] +
#                             plugin_list + ["parc_size", "issued_ips", "parc_size_sum"])
#     return df


# def create_data_pc_all(data_family_sub):
#     data_family_clust = self.calculate_up_entity("cluster", "family", data_family_sub)
#     data_family_div = self.calculate_up_entity("division", "family", data_family_clust)
#     data_family_group = self.calculate_up_entity("group", "family", data_family_div)

#     data_family = join_all_level("family",
#                                  data_family_sub,
#                                  data_family_clust,
#                                  data_family_div,
#                                  data_family_group)