import logging
import pandas as pd
import numpy as np
from src import DataImport, TableConstruction, GeneralConstruction, Correction, DataExport, ADrating
from src import send_csv_mail, config_logger, log_decorator, get_version_ref
from src.utils.utils import check_sources

# Initializing the logger
my_logger = logging.getLogger("CBR-MAIN")


@log_decorator("Step 1/9: Importing all databases ...")
def import_data() -> DataImport:
    """
    Runs the DataImport class

    Returns
    -------
    import_class : class
        The DataImport class
    """

    import_class = DataImport(fqdn=False)
    import_fqdn_class = DataImport(fqdn=True)
    return import_class, import_fqdn_class


@log_decorator("2-1: Initializing data ...")
def data_initialisation(tables_construction_class: TableConstruction,
                        tables_construction_fqdn: TableConstruction) -> None:
    """
    Runs the initializing_data function from TableConstruction class

    Parameters
    ----------
    tables_construction_class : class
        The TableConstruction class
    """

    tables_construction_class.initializing_data()
    tables_construction_fqdn.initializing_data()
    tables_construction_fqdn.data_ip_grade.rename(columns={"ip_id": "fqdn_id"}, inplace =True)
    # return tables_construction_class.data_ip_grade, tables_construction_fqdn.data_ip_grade


@log_decorator("2-2: Creating data category ...")
def data_category_creation(tables_construction_class: TableConstruction,
                           tables_construction_fqdn: TableConstruction) -> None:
    """
    Runs the create_data_category function from TableConstruction class

    Parameters
    ----------
    tables_construction_class : class
        The TableConstruction class
    """

    tables_construction_class.create_data_category()
    tables_construction_fqdn.create_data_category()


@log_decorator("2-3: Creating data family ...")
def data_family_creation(tables_construction_class: TableConstruction,
                         tables_construction_fqdn: TableConstruction) -> None:
    """
    Runs the create_data_family function from TableConstruction class

    Parameters
    ----------
    tables_construction_class : class
        The TableConstruction class
    """

    tables_construction_class.create_data_family()
    tables_construction_fqdn.create_data_family()


@log_decorator("2-4: Creating data total ...")
def data_total_creation(tables_construction_class: TableConstruction,
                        tables_construction_fqdn: TableConstruction) -> None:
    """
    Runs the create_data_total function from TableConstruction class

    Parameters
    ----------
    tables_construction_class : class
        The TableConstruction class
    """

    tables_construction_class.create_data_total()
    tables_construction_fqdn.create_data_total()


def privacy_tables_construction(privacy: str, import_class: DataImport,
                                import_fqdn_class: DataImport) -> TableConstruction:
    """
    Runs all the functions to create rating tables according to the privacy

    Parameters
    ----------
    privacy : str
        The ip privacy, public or private
    import_class : class
        The DataImport class

    Returns
    -------
    tables_construction_class : class
        The TableConstruction class according to the privacy
    """

    my_logger.info("Step 2/9: Rating on privacy: " + privacy)
    tables_construction_class = TableConstruction(privacy, import_class)
    tables_construction_fqdn = TableConstruction(privacy, import_fqdn_class)

    data_initialisation(tables_construction_class, tables_construction_fqdn)
    data_category_creation(tables_construction_class, tables_construction_fqdn)
    data_family_creation(tables_construction_class, tables_construction_fqdn)
    data_total_creation(tables_construction_class, tables_construction_fqdn)
    return tables_construction_class, tables_construction_fqdn


@log_decorator("3-1: Concatenating data category ...")
def data_category_concatenation(
        construct: GeneralConstruction,
        construct_fqdn: GeneralConstruction,
        public: TableConstruction,
        private: TableConstruction,
        public_fqdn: TableConstruction,
        private_fqdn: TableConstruction) -> pd.DataFrame:
    """
    Concatenates public and private category rating dataframe and creates a global category rating

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    construct_fqdn : class
        The GeneralConstruction class associate to fqdn
    public : class
        The TableConstruction class with public ips
    private : class
        The TableConstruction class with private ips
    public_fqdn : class
        The TableConstruction class with public fqdn
    private_fqdn : class
        The TableConstruction class with private fqdn

    Returns
    -------
    df_cat : DataFrame
        The category rating dataframe with global rating
    """

    my_logger = logging.getLogger("CBR-MAIN")

    # Defensive check: ensure data_category exists and is not None
    public_data_category = getattr(public, 'data_category', pd.DataFrame()) if public else pd.DataFrame()
    private_data_category = getattr(private, 'data_category', pd.DataFrame()) if private else pd.DataFrame()
    public_fqdn_data_category = getattr(public_fqdn, 'data_category', pd.DataFrame()) if public_fqdn else pd.DataFrame()
    private_fqdn_data_category = getattr(private_fqdn, 'data_category', pd.DataFrame()) if private_fqdn else pd.DataFrame()

    try:
        df = construct.create_all_level("category", public_data_category, private_data_category)
        # Defensive assignment with empty check
        if not df.empty:
            df = df.assign(resource_type=0)
        else:
            df = pd.DataFrame(columns=['resource_type'])
            df.loc[0, 'resource_type'] = 0

        df_fqdn = construct_fqdn.create_all_level("category", public_fqdn_data_category, private_fqdn_data_category)
        # Defensive assignment with empty check
        if not df_fqdn.empty:
            df_fqdn = df_fqdn.assign(resource_type=1)
        else:
            df_fqdn = pd.DataFrame(columns=['resource_type'])
            df_fqdn.loc[0, 'resource_type'] = 1

        df_cat = construct.merge_ip_fqdn(df, df_fqdn)

        # Defensive check: ensure result is not None
        if df_cat is None:
            my_logger.warning("merge_ip_fqdn returned None, creating empty DataFrame")
            df_cat = pd.DataFrame()

        return df_cat

    except Exception as e:
        my_logger.error(f"Error in data_category_concatenation: {e}")
        return pd.DataFrame()


@log_decorator("3-2: Concatenating data family ...")
def data_family_concatenation(
        construct: GeneralConstruction,
        construct_fqdn: GeneralConstruction,
        public: TableConstruction,
        private: TableConstruction,
        public_fqdn: TableConstruction,
        private_fqdn: TableConstruction) -> pd.DataFrame:
    """
    Concatenates public and private family rating dataframe and creates a global family rating

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    construct_fqdn : class
        The GeneralConstruction class associate to fqdn
    public : class
        The TableConstruction class with public ips
    private : class
        The TableConstruction class with private ips
    public_fqdn : class
        The TableConstruction class with public fqdn
    private_fqdn : class
        The TableConstruction class with private fqdn

    Returns
    -------
    df_fam : DataFrame
        The family rating dataframe with global rating
    """

    my_logger = logging.getLogger("CBR-MAIN")

    # Defensive check: ensure data_family exists and is not None
    public_data_family = getattr(public, 'data_family', pd.DataFrame()) if public else pd.DataFrame()
    private_data_family = getattr(private, 'data_family', pd.DataFrame()) if private else pd.DataFrame()
    public_fqdn_data_family = getattr(public_fqdn, 'data_family', pd.DataFrame()) if public_fqdn else pd.DataFrame()
    private_fqdn_data_family = getattr(private_fqdn, 'data_family', pd.DataFrame()) if private_fqdn else pd.DataFrame()

    try:
        df = construct.create_all_level("family", public_data_family, private_data_family)
        # Defensive assignment with empty check
        if not df.empty:
            df = df.assign(resource_type=0)
        else:
            df = pd.DataFrame(columns=['resource_type'])
            df.loc[0, 'resource_type'] = 0

        df_fqdn = construct_fqdn.create_all_level("family", public_fqdn_data_family, private_fqdn_data_family)
        # Defensive assignment with empty check
        if not df_fqdn.empty:
            df_fqdn = df_fqdn.assign(resource_type=1)
        else:
            df_fqdn = pd.DataFrame(columns=['resource_type'])
            df_fqdn.loc[0, 'resource_type'] = 1

        df_fam = construct.merge_ip_fqdn(df, df_fqdn)

        # Defensive check: ensure result is not None
        if df_fam is None:
            my_logger.warning("merge_ip_fqdn returned None, creating empty DataFrame")
            df_fam = pd.DataFrame()

        return df_fam

    except Exception as e:
        my_logger.error(f"Error in data_family_concatenation: {e}")
        return pd.DataFrame()


@log_decorator("3-3: Concatenating data total ...")
def data_total_concatenation(
        construct: GeneralConstruction,
        construct_fqdn: GeneralConstruction,
        public: TableConstruction,
        private: TableConstruction,
        public_fqdn: TableConstruction,
        private_fqdn: TableConstruction) -> pd.DataFrame:
    """
    Concatenates public and private total rating dataframe and creates a global total rating

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    construct_fqdn : class
        The GeneralConstruction class associate to fqdn
    public : class
        The TableConstruction class with public ips
    private : class
        The TableConstruction class with private ips
    public_fqdn : class
        The TableConstruction class with public fqdn
    private_fqdn : class
        The TableConstruction class with private fqdn

    Returns
    -------
    df : DataFrame
        The total rating dataframe with global rating
    """

    my_logger = logging.getLogger("CBR-MAIN")

    # Defensive check: ensure data_total exists and is not None
    public_data_total = getattr(public, 'data_total', pd.DataFrame()) if public else pd.DataFrame()
    private_data_total = getattr(private, 'data_total', pd.DataFrame()) if private else pd.DataFrame()
    public_fqdn_data_total = getattr(public_fqdn, 'data_total', pd.DataFrame()) if public_fqdn else pd.DataFrame()
    private_fqdn_data_total = getattr(private_fqdn, 'data_total', pd.DataFrame()) if private_fqdn else pd.DataFrame()

    try:
        df = construct.create_all_level("total", public_data_total, private_data_total)
        # Defensive assignment with empty check
        if not df.empty:
            df = df.assign(resource_type=0)
        else:
            df = pd.DataFrame(columns=['resource_type'])
            df.loc[0, 'resource_type'] = 0

        df_fqdn = construct_fqdn.create_all_level("total", public_fqdn_data_total, private_fqdn_data_total)
        # Defensive assignment with empty check
        if not df_fqdn.empty:
            df_fqdn = df_fqdn.assign(resource_type=1)
        else:
            df_fqdn = pd.DataFrame(columns=['resource_type'])
            df_fqdn.loc[0, 'resource_type'] = 1

        df_total = construct.merge_ip_fqdn(df, df_fqdn)

        # Defensive check: ensure result is not None
        if df_total is None:
            my_logger.warning("merge_ip_fqdn returned None, creating empty DataFrame")
            df_total = pd.DataFrame()

        return df_total

    except Exception as e:
        my_logger.error(f"Error in data_total_concatenation: {e}")
        return pd.DataFrame()


@log_decorator("3-4: Attributing rating letters ...")
def rating_letters_attribution(
        construct: GeneralConstruction,
        data_category: pd.DataFrame,
        data_family: pd.DataFrame,
        data_total: pd.DataFrame):
    """
    Attributes rating letters to dataframes

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    data_category : DataFrame
        The category rating dataframe
    data_family : DataFrame
        The family rating dataframe
    data_total : DataFrame
        The total rating dataframe

    Returns
    -------
    data_category : DataFrame
        The category rating dataframe with rating letters
    data_family : DataFrame
        The family rating dataframe with rating letters
    data_total : DataFrame
        The total rating dataframe with rating letters
    """

    my_logger = logging.getLogger("CBR-MAIN")

    # Defensive check: ensure DataFrames are not None
    if data_category is None:
        data_category = pd.DataFrame()
    if data_family is None:
        data_family = pd.DataFrame()
    if data_total is None:
        data_total = pd.DataFrame()

    try:
        data_category = construct.attribute_rating_letters(data_category, "category")
        data_family = construct.attribute_rating_letters(data_family, "family")
        data_total = construct.attribute_rating_letters(data_total, "total")

        return data_category, data_family, data_total

    except Exception as e:
        my_logger.error(f"Error in rating_letters_attribution: {e}")
        # Return empty DataFrames with rating_letter column
        return (pd.DataFrame(columns=['rating_letter']),
                pd.DataFrame(columns=['rating_letter']),
                pd.DataFrame(columns=['rating_letter']))


@log_decorator("3-5: Creating data findings ...")
def findings_creation(construct: GeneralConstruction,
                      construct_fqdn: GeneralConstruction,
                      public: TableConstruction,
                      private: TableConstruction,
                      public_fqdn: TableConstruction,
                      private_fqdn: TableConstruction):
    """
    Concatenates public and private findings and findings assets dataframe

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    construct_fqdn : class
        The GeneralConstruction fqdn class
    public : class
        The TableConstruction class with public ips
    private : class
        The TableConstruction class with private ips
    public_fqdn : class
        The TableConstruction class with public fqdn
    private_fqdn : class
        The TableConstruction class with private fqdn

    Returns
    -------
    findings : DataFrame
        The dataframe with all scans for entities since 45 days
    findings_asset : DataFrame
        The dataframe with all scans for assets since 45 days
    findings_fqdn : DataFrame
        The dataframe with all scans for fqdn entities since 45 days
    findings_fqdn_asset : DataFrame
        The dataframe with all scans for fqdn assets since 45 days
    """

    findings = construct.create_data_findings("entities", public.data, private.data)
    findings_asset = construct.create_data_findings("asset", public.data_asset, private.data_asset)

    findings_fqdn = construct_fqdn.create_data_findings("entities", public_fqdn.data, private_fqdn.data)
    findings_fqdn.rename(columns={"ip_id": "fqdn_id"}, inplace=True)
    findings_fqdn_asset = construct_fqdn.create_data_findings("asset", public_fqdn.data_asset, private_fqdn.data_asset)
    findings_fqdn_asset.rename(columns={"ip_id": "fqdn_id"}, inplace=True)
    return findings, findings_asset, findings_fqdn, findings_fqdn_asset


@log_decorator("3-6: Creating data patching cadence ...")
def patching_cadence_creation(
        construct: GeneralConstruction,
        construct_fqdn: GeneralConstruction,
        public: TableConstruction,
        private: TableConstruction,
        public_fqdn: TableConstruction,
        private_fqdn: TableConstruction) -> pd.DataFrame:
    """
    Concatenates public and private patching cadence dataframe

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    construct_fqdn : class
        The GeneralConstruction fqdn class
    public : class
        The TableConstruction class with public ips
    private : class
        The TableConstruction class with private ips
    public_fqdn : class
        The TableConstruction class with public fqdn
    private_fqdn : class
        The TableConstruction class with private f
    Returns
    -------
    severity_pc_mean : DataFrame
        The dataframe about mean correction delay by severity
    pc_history : DataFrame
        The dataframe with all scans history and information about correction for ips
    pc_history_fqdn : DataFrame
        The dataframe with all scans history and information about correction for fqdn
    pc_rate : DataFrame
        The dataframe with about the proportion of correction by severity
    """
    pc_history = construct.create_pc_db(public.pc_history, private.pc_history)
    pc_history.drop(["level_6", "level_8"], axis=1, inplace=True, errors="ignore")
    public.create_severity_pc_mean_table()
    private.create_severity_pc_mean_table()
    severity_pc_mean = construct.create_pc_db(public.pc_db_sm, private.pc_db_sm)
    severity_pc_mean = severity_pc_mean.assign(resource_type=0)
    public.create_correction_rate()
    private.create_correction_rate()
    pc_rate = construct.create_pc_db(public.pc_rate, private.pc_rate)

    pc_history_fqdn = construct_fqdn.create_pc_db(public_fqdn.pc_history, private_fqdn.pc_history)
    pc_history_fqdn.drop(["level_6", "level_8"], axis=1, inplace=True, errors="ignore")
    pc_history_fqdn.rename(columns={"ip_id": "fqdn_id"}, inplace=True)
    public_fqdn.create_severity_pc_mean_table()
    private_fqdn.create_severity_pc_mean_table()
    severity_pc_mean_fqdn = construct_fqdn.create_pc_db(public_fqdn.pc_db_sm, private_fqdn.pc_db_sm)
    severity_pc_mean_fqdn = severity_pc_mean_fqdn.assign(resource_type=1)

    severity_pc_mean = construct.merge_ip_fqdn(severity_pc_mean, severity_pc_mean_fqdn)
    return severity_pc_mean, pc_history, pc_history_fqdn, pc_rate


@log_decorator("3-7: Preparing tables for mail ...")
def mails_preparation(
        construct: GeneralConstruction,
        public: TableConstruction,
        private: TableConstruction,
        df_total: pd.DataFrame,
        pc_rate: pd.DataFrame
        ) -> None:
    """
    Creates csv sent by email

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    public : class
        The TableConstruction class with public ips
    private : class
        The TableConstruction class with private ips
    df_total : DataFrame
        The dataframe with total rating
    """

    construct.create_data_today_csv(df_total)
    construct.create_vulnerable_ips_csv(public.vulnerable_ips, private.vulnerable_ips)
    construct.create_clean_csv_cor_rate(pc_rate, "on_time_correction_rate")


def create_tables(import_class: DataImport, import_fqdn_class: DataImport):
    """
    Runs all the functions to create rating tables

    Parameters
    ----------
    import_class : class
        The DataImport class

    Returns
    -------
    data_category : DataFrame
        The category rating dataframe
    data_family: DataFrame
        The family rating dataframe
    data_total : DataFrame
        The total rating dataframe
    findings : DataFrame
        The dataframe with all scans for entities since 45 days
    findings_asset : DataFrame
        The dataframe with all scans for assets since 45 days
    patching_cadence : DataFrame
        The dataframe with all scans and information about mean correction delay
    public_class : TableConstruction
        The TableConstruction class with public ips
    private_class : TableConstruction
        The TableConstruction class with private ips
    """

    construct = GeneralConstruction(import_class)
    construct_fqdn = GeneralConstruction(import_fqdn_class)

    public_class, public_fqdn_class = privacy_tables_construction("public", import_class, import_fqdn_class)
    private_class, private_fqdn_class = privacy_tables_construction("private", import_class, import_fqdn_class)

    my_logger.info("Step 3/9: Preparing rating table")
    df_category = data_category_concatenation(construct, construct_fqdn,
                                              public_class, private_class,
                                              public_fqdn_class, private_fqdn_class)
    df_family = data_family_concatenation(construct, construct_fqdn,
                                          public_class, private_class,
                                          public_fqdn_class, private_fqdn_class)
    df_total = data_total_concatenation(construct, construct_fqdn,
                                        public_class, private_class,
                                        public_fqdn_class, private_fqdn_class)

    df_category, df_family, df_total = rating_letters_attribution(
        construct, df_category, df_family, df_total)

    findings, findings_asset, findings_fqdn, findings_fqdn_asset = findings_creation(
        construct, construct_fqdn, public_class, private_class, public_fqdn_class, private_fqdn_class)

    severity_pc_mean, pc_history, pc_history_fqdn, pc_rate = patching_cadence_creation(
        construct, construct_fqdn, public_class, private_class, public_fqdn_class, private_fqdn_class)

    mails_preparation(construct, public_class, private_class, df_total, pc_rate)
    return [df_category, df_family, df_total, findings,
            findings_asset, findings_fqdn, findings_fqdn_asset,
            pc_history, pc_history_fqdn, severity_pc_mean, public_class,
            private_class, public_fqdn_class, private_fqdn_class]


@log_decorator("Step 4/9: Build & Export AD scoring ...")
def build_ad_rating():
    """
    Build AD rating from data extraction to rating contruction_
    """
    ADrating()


@log_decorator("Step 5/9: Exporting rating tables ...")
def export_rating_tables(
        dc: pd.DataFrame,
        df: pd.DataFrame,
        dt: pd.DataFrame,
        dff: pd.DataFrame,
        dff_fqdn: pd.DataFrame,
        dfa_fqdn: pd.DataFrame,
        dfa: pd.DataFrame,
        pch: pd.DataFrame,
        pch_fqdn: pd.DataFrame,
        pcm: pd.DataFrame) -> None:
    """
    Exports the rating tables with rating_tables_export function from DataExport class

    Parameters
    ----------
    dc : DataFrame
        The category rating dataframe
    df: DataFrame
        The family rating dataframe
    dt : DataFrame
        The total rating dataframe
    dff : DataFrame
        The dataframe with all scans for entities since 45 days about ip
    dfa : DataFrame
        The dataframe with all scans for assets since 45 days about ip
    dff_fqdn :
        The dataframe with all scans for assets since 45 days about fqdn
    dfa_fqdn :
        The dataframe with all scans for assets since 45 days about fqdn
    pch : DataFrame
        The dataframe with all history about of vulnerabilities apparition about ip
    pch_fqdn : DataFrame
        The dataframe with all history about of vulnerabilities apparition about fqdn
    pcm : DataFrame
        The dataframe with mean correction delay by severity and level
    """

    export_class = DataExport()
    export_class.rating_tables_export(dc, df, dt, dff, dff_fqdn, dfa_fqdn, dfa, pch, pch_fqdn, pcm)


@log_decorator("Step 6/9: Sending mail ...")
def send_mail() -> None:
    """
    Sends the mails with rating of the day and vulnerable ips
    """
    send_csv_mail("data_today.csv")
    send_csv_mail("vulnerable_ips.csv")
    send_csv_mail("test")


@log_decorator("7-1: Creating category correction table ...")
def category_correction(
        construct: GeneralConstruction,
        construct_fqdn: GeneralConstruction,
        pub_correction_class: Correction,
        priv_correction_class: Correction,
        pub_correction_fqdn_class: Correction,
        priv_correction_fqdn_class: Correction) -> tuple:
    """
    Creates public and private category correction dataframe and concatenates them

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    pub_correction_class : class
        The Correction class with public ips
    priv_correction_class : class
        The Correction class with private ips

    Returns
    -------
    out : DataFrame
        The dataframe with category correction
    """
    
    try:
        my_logger.info("Starting category_correction function")
        
        my_logger.info("Creating public category correction...")
        cat_cor_pub = pub_correction_class.create_privacy_plugin_correction("category")
        my_logger.info(f"Public category correction created with shape: {cat_cor_pub.shape if hasattr(cat_cor_pub, 'shape') else 'No shape'}")
        
        my_logger.info("Creating private category correction...")
        cat_cor_priv = priv_correction_class.create_privacy_plugin_correction("category")
        my_logger.info(f"Private category correction created with shape: {cat_cor_priv.shape if hasattr(cat_cor_priv, 'shape') else 'No shape'}")
        
        my_logger.info("Creating all category correction...")
        cat_cor_all = pub_correction_class.create_plugin_correction(construct, "category", cat_cor_pub, cat_cor_priv)
        my_logger.info(f"All category correction created with shape: {cat_cor_all.shape if hasattr(cat_cor_all, 'shape') else 'No shape'}")
        
        cat_cor_all = cat_cor_all.assign(privacy="all")
        my_logger.info("Privacy column assigned to all category correction")
        
        my_logger.info("Concatenating public, private and all category corrections...")
        out = pd.concat([cat_cor_pub, cat_cor_priv, cat_cor_all], ignore_index=True)[cat_cor_all.columns]
        my_logger.info(f"Main category correction concatenated with shape: {out.shape}")
        
        my_logger.info("Applying cluster rating corrections...")
        out["cluster_rating_if_corr"] = np.where(out["cluster"] >= 1000, np.nan, out["cluster_rating_if_corr"])
        out["cluster"] = np.where(out["cluster"] >= 1000, np.nan, out["cluster"])
        my_logger.info("Cluster rating corrections applied")

        my_logger.info("Creating public FQDN category correction...")
        cat_cor_pub_fqdn = pub_correction_fqdn_class.create_privacy_plugin_correction("category")
        my_logger.info(f"Public FQDN category correction created with shape: {cat_cor_pub_fqdn.shape if hasattr(cat_cor_pub_fqdn, 'shape') else 'No shape'}")
        
        my_logger.info("Creating private FQDN category correction...")
        cat_cor_priv_fqdn = priv_correction_fqdn_class.create_privacy_plugin_correction("category")
        my_logger.info(f"Private FQDN category correction created with shape: {cat_cor_priv_fqdn.shape if hasattr(cat_cor_priv_fqdn, 'shape') else 'No shape'}")
        
        my_logger.info("Creating all FQDN category correction...")
        cat_cor_all_fqdn = pub_correction_class.create_plugin_correction(construct_fqdn, "category", cat_cor_pub_fqdn, cat_cor_priv_fqdn)
        my_logger.info(f"All FQDN category correction created with shape: {cat_cor_all_fqdn.shape if hasattr(cat_cor_all_fqdn, 'shape') else 'No shape'}")
        
        cat_cor_all_fqdn = cat_cor_all_fqdn.assign(privacy="all")
        my_logger.info("Privacy column assigned to all FQDN category correction")
        
        my_logger.info("Concatenating FQDN category corrections...")
        out_fqdn = pd.concat([cat_cor_pub_fqdn, cat_cor_priv_fqdn, cat_cor_all_fqdn], ignore_index=True)[cat_cor_all.columns]
        my_logger.info(f"FQDN category correction concatenated with shape: {out_fqdn.shape}")
        
        my_logger.info("Applying FQDN cluster rating corrections...")
        out_fqdn["cluster_rating_if_corr"] = np.where(out_fqdn["cluster"] >= 1000, np.nan, out_fqdn["cluster_rating_if_corr"])
        out_fqdn["cluster"] = np.where(out_fqdn["cluster"] >= 1000, np.nan, out_fqdn["cluster"])
        my_logger.info("FQDN cluster rating corrections applied")

        my_logger.info("Category correction function completed successfully")
        return out, out_fqdn
        
    except Exception as e:
        my_logger.error(f"Error in category_correction: {str(e)}")
        my_logger.error(f"Exception type: {type(e).__name__}")
        import traceback
        my_logger.error(f"Full traceback: {traceback.format_exc()}")
        # Return empty DataFrames with expected structure instead of None
        empty_df = pd.DataFrame()
        return empty_df, empty_df


@log_decorator("7-2: Creating family correction table ...")
def family_correction(
        construct: GeneralConstruction,
        construct_fqdn: GeneralConstruction,
        pub_correction_class: Correction,
        priv_correction_class: Correction,
        pub_correction_fqdn_class: Correction,
        priv_correction_fqdn_class: Correction) -> tuple:
    """
    Creates public and private family correction dataframe and concatenates them
    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    pub_correction_class : class
        The Correction class with public ips
    priv_correction_class : class
        The Correction class with private ips

    Returns
    -------
    out : DataFrame
        The dataframe with family correction
    """
    
    try:
        fam_cor_pub = pub_correction_class.create_privacy_plugin_correction("family")
        fam_cor_priv = priv_correction_class.create_privacy_plugin_correction("family")
        fam_cor_all = pub_correction_class.create_plugin_correction(construct, "family", fam_cor_pub, fam_cor_priv)
        fam_cor_all = fam_cor_all.assign(privacy="all")
        out = pd.concat([fam_cor_pub, fam_cor_priv, fam_cor_all], ignore_index=True)[fam_cor_all.columns]
        out["cluster_rating_if_corr"] = np.where(out["cluster"] >= 1000, np.nan, out["cluster_rating_if_corr"])
        out["cluster"] = np.where(out["cluster"] >= 1000, np.nan, out["cluster"])

        fam_cor_pub_fqdn = pub_correction_fqdn_class.create_privacy_plugin_correction("family")
        fam_cor_priv_fqdn = priv_correction_fqdn_class.create_privacy_plugin_correction("family")
        fam_cor_all_fqdn = pub_correction_fqdn_class.create_plugin_correction(construct_fqdn, "family", fam_cor_pub_fqdn, fam_cor_priv_fqdn)
        fam_cor_all_fqdn = fam_cor_all_fqdn.assign(privacy="all")
        out_fqdn = pd.concat([fam_cor_pub_fqdn, fam_cor_priv_fqdn, fam_cor_all_fqdn], ignore_index=True)[fam_cor_all_fqdn.columns]
        out_fqdn["cluster_rating_if_corr"] = np.where(out_fqdn["cluster"] >= 1000, np.nan, out_fqdn["cluster_rating_if_corr"])
        out_fqdn["cluster"] = np.where(out_fqdn["cluster"] >= 1000, np.nan, out_fqdn["cluster"])
        
        return out, out_fqdn
        
    except Exception as e:
        my_logger.error(f"Error in family_correction: {str(e)}")
        empty_df = pd.DataFrame()
        return empty_df, empty_df


@log_decorator("7-3: Creating ip correction table ...")
def ip_correction(
        construct: GeneralConstruction,
        construct_fqdn: GeneralConstruction,
        pub_correction_class: Correction,
        priv_correction_class: Correction,
        pub_correction_fqdn_class: Correction,
        priv_correction_fqdn_class: Correction) -> pd.DataFrame:
    """
    Creates public and private ip correction dataframe and concatenates them

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    pub_correction_class : class
        The Correction class with public ips
    priv_correction_class : class
        The Correction class with private ips

    Returns
    -------
    out : DataFrame
        The dataframe with ip correction
    """

    pub = pub_correction_class.create_ip_correction()
    priv = priv_correction_class.create_ip_correction()
    all = pub_correction_class.create_plugin_correction(construct, "category", pub, priv, rt_correction=True)
    all = all.assign(privacy="all")
    out = pd.concat([pub, priv, all], ignore_index=True)[all.columns]
    out["cat_cluster_rating_if_corr"] = np.where(out["cluster"] >= 1000, np.nan, out["cat_cluster_rating_if_corr"])
    out["cluster"] = np.where(out["cluster"] >= 1000, np.nan, out["cluster"])

    pub_fqdn = pub_correction_fqdn_class.create_ip_correction()
    priv_fqdn = priv_correction_fqdn_class.create_ip_correction()

    all_fqdn = pub_correction_fqdn_class.create_plugin_correction(construct_fqdn, "category", pub_fqdn, priv_fqdn, rt_correction=True)
    all_fqdn = all_fqdn.assign(privacy="all")
    out_fqdn = pd.concat([pub_fqdn, priv_fqdn, all_fqdn], ignore_index=True)[all_fqdn.columns]
    out_fqdn.rename(columns={"ip_id": "fqdn_id"}, inplace=True)

    out_fqdn["cat_cluster_rating_if_corr"] = np.where(out_fqdn["cluster"] >= 1000, np.nan, out_fqdn["cat_cluster_rating_if_corr"])
    out_fqdn["cluster"] = np.where(out_fqdn["cluster"] >= 1000, np.nan, out_fqdn["cluster"])

    return out, out_fqdn


def create_correction_table(
        import_class: DataImport,
        import_fqdn_class: DataImport,
        public_class: TableConstruction,
        private_class: TableConstruction,
        public_fqdn_class: TableConstruction,
        private_fqdn_class: TableConstruction):
    """
    Runs all the functions to create the correction dataframes

    Parameters
    ----------
    import_class : class
        The DataImport class
    import_fqdn_class : class
        The DataImport fqdn class
    public_class : class
        The TableConstruction class with public ips
    private_class : class
        The TableConstruction class with private ips
    public_class : class
        The TableConstruction class with public fqdn
    private_class : class
        The TableConstruction class with private fqdn

    Returns
    -------
    data_category_correction : DataFrame
        The dataframe with category correction
    data_family_correction : DataFrame
        The dataframe with family correction
    data_ip_correction : DataFrame
        The dataframe with ip correction
    data_fqdn_correction : DataFrame
        The dataframe with fqdn correction
    """

    my_logger.info("Step 7/9: Creating correction tables")
    
    try:
        my_logger.info("Creating Correction class instances...")
        pub_correction_class = Correction(public_class)
        priv_correction_class = Correction(private_class)
        pub_correction_fqdn_class = Correction(public_fqdn_class)
        priv_correction_fqdn_class = Correction(private_fqdn_class)
        my_logger.info("Correction class instances created successfully")
        
        my_logger.info("Creating GeneralConstruction class instances...")
        construct = GeneralConstruction(import_class)
        construct_fqdn = GeneralConstruction(import_fqdn_class)
        my_logger.info("GeneralConstruction class instances created successfully")

        # Validate correction table creation
        my_logger.info("Starting category correction...")
        data_category_correction, data_category_correction_fqdn = category_correction(
            construct, construct_fqdn, pub_correction_class, priv_correction_class, 
            pub_correction_fqdn_class, priv_correction_fqdn_class)
        my_logger.info("Category correction completed")
        
        if data_category_correction is None or data_category_correction_fqdn is None:
            my_logger.error("Category correction returned None - using empty DataFrames")
            data_category_correction = pd.DataFrame()
            data_category_correction_fqdn = pd.DataFrame()

        my_logger.info("Starting family correction...")
        data_family_correction, data_family_correction_fqdn = family_correction(
            construct, construct_fqdn, pub_correction_class, priv_correction_class, 
            pub_correction_fqdn_class, priv_correction_fqdn_class)
        my_logger.info("Family correction completed")
        
        if data_family_correction is None or data_family_correction_fqdn is None:
            my_logger.error("Family correction returned None - using empty DataFrames")
            data_family_correction = pd.DataFrame()
            data_family_correction_fqdn = pd.DataFrame()

        my_logger.info("Starting IP correction...")
        data_ip_correction, data_ip_correction_fqdn = ip_correction(
            construct, construct_fqdn, pub_correction_class, priv_correction_class, 
            pub_correction_fqdn_class, priv_correction_fqdn_class)
        my_logger.info("IP correction completed")
        
        if data_ip_correction is None or data_ip_correction_fqdn is None:
            my_logger.error("IP correction returned None - using empty DataFrames")
            data_ip_correction = pd.DataFrame()
            data_ip_correction_fqdn = pd.DataFrame()

        # Safe assignment with empty DataFrame handling
        my_logger.info("Processing category correction results...")
        if not data_category_correction.empty:
            data_category_correction = data_category_correction.assign(resource_type=0)
            my_logger.info("Resource type 0 assigned to category correction")
        if not data_category_correction_fqdn.empty:
            data_category_correction_fqdn = data_category_correction_fqdn.assign(resource_type=1)
            my_logger.info("Resource type 1 assigned to FQDN category correction")
            data_category_correction = construct.merge_ip_fqdn(data_category_correction, data_category_correction_fqdn)
            my_logger.info("Category corrections merged")
        
        my_logger.info("Processing family correction results...")
        if not data_family_correction.empty:
            data_family_correction = data_family_correction.assign(resource_type=0)
            my_logger.info("Resource type 0 assigned to family correction")
        if not data_family_correction_fqdn.empty:
            data_family_correction_fqdn = data_family_correction_fqdn.assign(resource_type=1)
            my_logger.info("Resource type 1 assigned to FQDN family correction")
            data_family_correction = construct.merge_ip_fqdn(data_family_correction, data_family_correction_fqdn)
            my_logger.info("Family corrections merged")

        my_logger.info("Correction table creation completed successfully")
        return data_category_correction, data_family_correction, data_ip_correction, data_ip_correction_fqdn
        
    except Exception as e:
        my_logger.error(f"Critical error in create_correction_table: {str(e)}")
        my_logger.error(f"Exception type: {type(e).__name__}")
        import traceback
        my_logger.error(f"Full traceback: {traceback.format_exc()}")
        # Return empty DataFrames to prevent unpacking errors
        empty_df = pd.DataFrame()
        return empty_df, empty_df, empty_df, empty_df


@log_decorator("Step 8/9: Exporting correction tables ...")
def export_correction_table(category: pd.DataFrame, family: pd.DataFrame,
                            ip: pd.DataFrame, fqdn: pd.DataFrame) -> None:
    """
    Exports the correction tables with correction_tables_export function from DataExport class

    Parameters
    ----------
    category : DataFrame
        The dataframe with category correction
    family: DataFrame
        The dataframe with family correction
    ip : DataFrame
        The dataframe with ip correction
    fqdn : DataFrame
        The dataframe with fqdn correction
    """

    export_class = DataExport()
    export_class.correction_tables_export(category, family, ip, fqdn)


@log_decorator("Step 9/9: Checking the day's data  ...")
def check_data() -> None:
    """
    Check that the day's data is written to the tables
    """
    export_class = DataExport()
    export_class.table_write_test()


# # TODO: remove after update schema
def useless_col_schema(category, family, total):
    category = category.assign(findings_counter=0)
    family = family.assign(findings_counter=0)
    total = total.assign(findings_counter=0)
    return category, family, total


# # TODO: remove after update schema
def useless_col_schema2(cat_cor, fam_cor, ip_cor, ip_cor_fqdn):
    fam_cor = fam_cor.assign(findings_counter=0)
    fam_cor = fam_cor.assign(weight=0)
    cat_cor = cat_cor.assign(findings_counter=0)
    cat_cor = cat_cor.assign(weight=0)
    ip_cor = ip_cor.assign(findings_counter=0)
    ip_cor_fqdn = ip_cor_fqdn.assign(findings_counter=0)
    return cat_cor, fam_cor, ip_cor, ip_cor_fqdn


def main() -> None:
    """
    Runs all functions to run the algorithm
    """
    config_logger()
    logging.basicConfig(level=logging.INFO)
    my_logger.info("Lancement du moteur de notation IA (" + get_version_ref()[0] + " - " + get_version_ref()[1] + ")")
    
    # Get all source data from the database
    import_class, import_fqdn_class = import_data()

    # Check if our source data is structured (column check) and dataframes are populated
    check_sources(import_class, my_logger, prefix="[IP] ")
    check_sources(import_fqdn_class, my_logger, prefix="[FQDN] ")

    # Build and export all data
    dc, df, dt, dff, dfa, dff_fqdn, dfa_fqdn, pc_h, pch_fqdn, pc_m, public_class, private_class, public_fqdn_class, private_fqdn_class = create_tables(import_class, import_fqdn_class)
    dc, df, dt = useless_col_schema(dc, df, dt)
    build_ad_rating()
    export_rating_tables(dc, df, dt, dff, dfa, dff_fqdn, dfa_fqdn, pc_h, pch_fqdn, pc_m)
    send_mail()
    cat_cor, fam_cor, ip_cor, ip_cor_fqdn = create_correction_table(
        import_class, import_fqdn_class,
        public_class, private_class,
        public_fqdn_class, private_fqdn_class)
    cat_cor, fam_cor, ip_cor, ip_cor_fqdn = useless_col_schema2(cat_cor, fam_cor, ip_cor, ip_cor_fqdn)
    # # TODO: voir export avec Fred
    export_correction_table(cat_cor, fam_cor, ip_cor, ip_cor_fqdn)

    # Check and inform on which tables have been updated to the database
    check_data()

if __name__ == "__main__":
    main()
