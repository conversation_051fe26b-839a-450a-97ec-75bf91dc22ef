#!/usr/bin/env python3
"""
Test script to verify defensive programming patterns work correctly with empty data.
This script simulates the scenario where input data tables are empty and verifies
that the pipeline runs without crashing.
"""

import sys
import os
import pandas as pd
import logging
from datetime import date, timedelta

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.import_data import DataImport
from src.table_construction import TableConstruction, GeneralConstruction
from src.utils.utils import safe_merge_with_type_conversion, standardize_data_types

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_mock_data_import():
    """Create a mock DataImport object with empty DataFrames to simulate missing data."""
    
    class MockDataImport:
        def __init__(self):
            # Initialize all attributes with empty DataFrames
            self.tenable_plugin = pd.DataFrame(columns=['id', 'tenable_plugin_id', 'severity'])
            self.ref_rt_asset = pd.DataFrame(columns=["ip_id", "asset", "id", "is_public"])
            self.db_category = pd.DataFrame(columns=[
                'privacy', 'level', 'category', 'subsidiary', 'family', 'resource_type', 
                'cluster', 'division', 'asset', 'group'
            ])
            self.ref_severity = pd.DataFrame(columns=['id', 'name'])
            self.asset = pd.DataFrame(columns=['id', 'name'])
            self.subsidiary = pd.DataFrame(columns=['id', 'name'])
            self.cluster = pd.DataFrame(columns=['id', 'name'])
            self.division = pd.DataFrame(columns=['id', 'name'])
            self.category = pd.DataFrame(columns=['id', 'name', 'orange_cyber_rating_family_id', 'activate'])
            self.family = pd.DataFrame(columns=['id', 'name', 'activate'])
            self.mapping = pd.DataFrame(columns=['field_value', 'orange_cyber_rating_category_id', 'orange_cyber_rating_family_id'])
            self.ref_rt = pd.DataFrame(columns=["id", "is_public", "subsidiary", "cluster", "division"])
            self.table_pc_history = pd.DataFrame(columns=[
                'ip_id', 'plugin_id', 'scan_date', 'max_scan_date', 'correction', 
                'correction_delay', 'patching_cadence_days', 'division', 'cluster', 
                'subsidiary', 'asset', 'severity'
            ])
            self.fqdn_scan_result = pd.DataFrame()
            self.ref_fqdn = pd.DataFrame()
            self.plugin_excluded_from_rating = pd.DataFrame(columns=['ip_id', 'plugin_id'])
            self.plugin_excluded_from_pc = pd.DataFrame(columns=['ip_id', 'plugin_id'])
            self.rating_letters = pd.DataFrame(columns=['id', 'value'])
            self.pc_scan_result = pd.DataFrame(columns=[
                'scan_date', 'ip_id', 'plugin_id', 'subsidiary', 'cluster', 'division'
            ])
            self.table_data_family = pd.DataFrame()
            
            # Set other required attributes
            self.list_ip_type = []
            self.scan_date = 30
            self.ip_penalty_th = 0.5
            self.removed_sub = []
            self.removed_clust = []
            self.removed_div = []
            self.healthy_ip_th = 100.0
            
    return MockDataImport()

def test_table_construction_with_empty_data():
    """Test TableConstruction with empty data."""
    print("\n=== Testing TableConstruction with empty data ===")
    
    mock_di = create_mock_data_import()
    
    try:
        # Test public privacy
        tc_public = TableConstruction("public", mock_di)
        print("✓ TableConstruction initialization with empty data succeeded")
        
        # Test preparing_data method
        tc_public.preparing_data()
        print("✓ preparing_data with empty data succeeded")
        
        # Test data creation methods
        tc_public.create_data_category()
        print("✓ create_data_category with empty data succeeded")
        
        tc_public.create_data_family()
        print("✓ create_data_family with empty data succeeded")
        
        tc_public.create_data_total()
        print("✓ create_data_total with empty data succeeded")
        
        # Verify that all data attributes are empty DataFrames (not None)
        assert hasattr(tc_public, 'data_category'), "data_category attribute missing"
        assert hasattr(tc_public, 'data_family'), "data_family attribute missing"
        assert hasattr(tc_public, 'data_total'), "data_total attribute missing"
        
        print("✓ All data attributes exist and are not None")
        
    except Exception as e:
        print(f"✗ TableConstruction test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    return True

def test_general_construction_with_empty_data():
    """Test GeneralConstruction methods with empty data."""
    print("\n=== Testing GeneralConstruction with empty data ===")
    
    mock_di = create_mock_data_import()
    
    try:
        # Create a mock GeneralConstruction instance
        gc = GeneralConstruction()
        gc.rating_letters = pd.DataFrame(columns=['id', 'value'])
        gc.list_level = ['subsidiary', 'cluster', 'division']
        
        # Test create_all_level with empty DataFrames
        result = gc.create_all_level("category", pd.DataFrame(), pd.DataFrame())
        print("✓ create_all_level with empty data succeeded")
        
        # Test merge_ip_fqdn with empty DataFrames
        result = gc.merge_ip_fqdn(pd.DataFrame(), pd.DataFrame())
        print("✓ merge_ip_fqdn with empty data succeeded")
        
        # Test attribute_rating_letters with empty DataFrame
        result = gc.attribute_rating_letters(pd.DataFrame(), "category")
        print("✓ attribute_rating_letters with empty data succeeded")
        
    except Exception as e:
        print(f"✗ GeneralConstruction test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    return True

def test_safe_merge_functions():
    """Test the safe merge utility functions."""
    print("\n=== Testing safe merge utility functions ===")
    
    try:
        # Test safe_merge_with_type_conversion with empty DataFrames
        result = safe_merge_with_type_conversion(pd.DataFrame(), pd.DataFrame(), on='id')
        assert result.empty, "Expected empty result from empty DataFrames"
        print("✓ safe_merge_with_type_conversion with empty data succeeded")
        
        # Test standardize_data_types with empty DataFrame
        result = standardize_data_types(pd.DataFrame())
        assert result.empty, "Expected empty result from empty DataFrame"
        print("✓ standardize_data_types with empty data succeeded")
        
        # Test with DataFrames that have mismatched types
        df1 = pd.DataFrame({'id': [1, 2, 3], 'value': ['a', 'b', 'c']})
        df2 = pd.DataFrame({'id': ['1', '2', '4'], 'other': ['x', 'y', 'z']})
        
        result = safe_merge_with_type_conversion(df1, df2, on='id')
        print(f"✓ safe_merge_with_type_conversion with type mismatch succeeded, result shape: {result.shape}")
        
    except Exception as e:
        print(f"✗ Safe merge functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    return True

def main():
    """Run all defensive pattern tests."""
    print("Starting defensive programming pattern tests...")
    
    all_tests_passed = True
    
    # Test individual components
    all_tests_passed &= test_safe_merge_functions()
    all_tests_passed &= test_table_construction_with_empty_data()
    all_tests_passed &= test_general_construction_with_empty_data()
    
    if all_tests_passed:
        print("\n🎉 All defensive pattern tests PASSED!")
        print("The pipeline should now handle empty data gracefully without crashing.")
    else:
        print("\n❌ Some defensive pattern tests FAILED!")
        print("Additional work may be needed to handle all edge cases.")
        
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
