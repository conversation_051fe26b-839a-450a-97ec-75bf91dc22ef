# Pipeline stages
stages:  
  - build
  - test
  - package-build
  - package-test
  - infra
  - deploy
  - acceptance
  - publish
  - infra-prod
  - production

sonarqube-check:
  image: 
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script: 
    - sonar-scanner
  allow_failure: true
  only:
    - merge_requests
    - main # or the name of your main branch
    - develop

upload:
  image: curlimages/curl:latest
  stage: publish
  script:
    - tar -C IA_Cyberrating -zcvf ia_cbr.tar.gz scripts/
    - ls -l
    - '[ -z "${CI_COMMIT_TAG}" ] && VERSION=${CI_COMMIT_REF_NAME} || VERSION=${CI_COMMIT_TAG}'
    - 'curl -v --header "JOB-TOKEN: $CI_JOB_TOKEN" --upload-file ia_cbr.tar.gz "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/generic/ia-cyber-rating/$VERSION/ia_cbr.tar.gz"'
  only:
    - main
    - dev
    - pre_prod
    - tags