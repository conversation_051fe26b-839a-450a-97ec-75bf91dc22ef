# -*- coding: utf-8 -*-
"""
Created on Tue May  3 17:53:42 2022

@author: VMZB5164
"""

import logging


class LogConfig:
    """ Constructor of the class"""
    def __init__(self):
        pass  # initialize the class

    @staticmethod
    def start_logging(file='executeMain'):

        logging.basicConfig(filename='logs/{}.log'.format(file),
                            format='%(asctime)s - [%(levelname)s] - %(message)s',
                            datefmt='%Y-%m-%d %H:%M:%S',
                            encoding='utf-8',
                            filemode='w',
                            level=logging.INFO)
        logging.info("Démarrage du traitement")
        logging.captureWarnings(True)
