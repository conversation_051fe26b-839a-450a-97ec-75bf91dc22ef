import pandas as pd
from datetime import datetime, timedelta
from utils import assign_pc_weight


def load_and_prepare_data():
    input_path = r"data/patching_history_0.csv"
    pc_df = pd.read_csv(input_path, sep=";", low_memory=False)
    pc_df["max_scan_date"] = pd.to_datetime(pc_df["max_scan_date"], dayfirst=True)
    pc_df["min_scan_date"] = pd.to_datetime(pc_df["min_scan_date"], dayfirst=True)
    ref_severity = pd.DataFrame({"severity": [4, 3, 2, 1, 0], "patching_cadence_days": [10, 30, 180, 1000, 1000]})
    pc_df_ = pc_df.merge(ref_severity[["severity", "patching_cadence_days"]], on="severity")
    subsidiaries = pc_df_["subsidiary"].unique().tolist()
    return pc_df_, subsidiaries


def add_penalty(pc_df, gain_values, lose_values, factor_values, date_now=datetime.now(), real_value=False):
    delta_year = date_now - timedelta(days=365)
    pc_df = pc_df[pc_df["subsidiary"].notna()]
    entities_list = ["division", "cluster", "subsidiary"]
    pc_df.loc[:, "max_scan_date"] = pd.to_datetime(pc_df["max_scan_date"])
    # Sous-ensemble où correction == False
    pc_df.loc[:, "correction"] = pc_df["correction"].astype(bool)
    df_correction_false = pc_df[~pc_df["correction"]]
    # Sous-ensemble où correction == True et max_scan_date >= delta_year
    df_correction_true = pc_df[(pc_df["correction"])]
    df_correction_true = df_correction_true[(df_correction_true["max_scan_date"] >= delta_year)]

    # Concaténer les deux sous-ensembles
    pc_df = pd.concat([df_correction_false, df_correction_true])

    pc_df.loc[:, "penalty"] = pc_df.apply(
        lambda row: assign_pc_weight(
            row["correction"],
            row["correction_delay"],
            row["patching_cadence_days"],
            row["severity"],
            row["max_scan_date"],
            pc_penalty_lose=lose_values,
            pc_penalty_gain=gain_values,
            pc_factor_max=factor_values,
            date_now=date_now
        ),
        axis=1)
    return pc_df


# def on_time_correction_rate(pc_df):
#     pc = pc_df[pc_df["severity"] != 1].copy()
#     # 1. Marquage des vulnérabilités à inclure
#     # - corrigées (correction == True)
#     # - non corrigées ET hors délai
#     pc['considered'] = pc['correction'] | (
#         (~pc['correction']) & (pc['correction_delay'] > pc['patching_cadence_days'])
#     )

#     # 2. Marquage des vulnérabilités corrigées dans les temps
#     pc['on_time_fix'] = pc['correction'] & (pc['correction_delay'] <= pc['patching_cadence_days'])

#     # 3. Agrégation par sévérité
#     grouped = pc[pc['considered']].groupby('severity').agg(
#         total_considered=('considered', 'count'),
#         fixed_on_time=('on_time_fix', 'sum')
#     )

#     # 4. Calcul de la proportion
#     grouped['proportion_fixed_on_time'] = grouped['fixed_on_time'] / grouped['total_considered']

#     # Optionnel : trier par sévérité
#     grouped = grouped.sort_index()

#     # Affichage final
#     print(grouped[['proportion_fixed_on_time']])
#     print(pc.columns)

def compute_fix_proportions(df, group_col):
    # Proportions par sévérité
    severity_map = {'medium': 2, 'high': 3, 'critical': 4}
    reverse_severity_map = {v: k for k, v in severity_map.items()}
    severity_df = df[df['considered']].groupby([group_col, 'severity']).agg(
        total=('considered', 'count'),
        on_time=('on_time_fix', 'sum')
    ).reset_index()

    severity_df['prop_on_time'] = round((severity_df['on_time'] / severity_df['total']) * 100, 2)

    # Pivot pour colonnes par sévérité
    pivot_prop = severity_df.pivot(index=group_col, columns='severity', values='prop_on_time')
    pivot_prop = pivot_prop.rename(columns={
        lvl: f'prop_{reverse_severity_map[lvl]}_on_time' for lvl in pivot_prop.columns
    })

    # Pivot pour les volumes (nb de vulnérabilités considérées par sévérité)
    pivot_count = severity_df.pivot(index=group_col, columns='severity', values='total')
    pivot_count = pivot_count.rename(columns={
        lvl: f'count_{reverse_severity_map[lvl]}' for lvl in pivot_count.columns
    })

    #  Fusion des deux pivots
    pivot = pivot_prop.join(pivot_count).reset_index()

    # Proportion globale (sans distinction de sévérité)
    global_df = df[df['considered']].groupby(group_col).agg(
        total=('considered', 'count'),
        on_time=('on_time_fix', 'sum')
    ).reset_index()
    global_df['proportion_fixed_on_time'] = round((global_df['on_time'] / global_df['total']) * 100, 2)
    vuln_total_df = df.groupby(group_col).size().reset_index(name='vuln_total')
    vuln_count_df = df[df['considered']].groupby(group_col).size().reset_index(name='vuln_count')
    # Merge final
    result = pivot.merge(global_df[[group_col, 'proportion_fixed_on_time']], on=group_col)
    result = result.merge(vuln_count_df, on=group_col, how='left')
    result = result.merge(vuln_total_df, on=group_col, how='left')
    return result


def concat_all_levels(pc_df):
    # Liste des niveaux hiérarchiques à traiter
    levels = ['subsidiary', 'cluster', 'division', 'asset', 'group']
    all_results = []

    for level in levels:
        name_col = f"{level}_name"
        df = pc_df.copy()
        if level == "group":
            df = pc_df[pc_df["subsidiary"].notna()].copy()
            df.loc[:, "group_name"] = "Orange"
            df.loc[:, "group"] = "Orange"

        if level == "cluster":
            df = pc_df[pc_df["cluster"] < 1000].copy()
        # Appliquer la fonction précédente
        level_df = compute_fix_proportions(df, group_col=level)

        # Merge avec les noms (uniques, donc drop_duplicates)
        name_map = df[[level, name_col]].drop_duplicates()
        level_df = level_df.merge(name_map, on=level, how='left')

        # Ajout du niveau pour trace
        level_df['level'] = level

        # Harmonisation du nom d'entité (pour facilité de concaténation/affichage)
        level_df['entity_id'] = level_df[level]
        level_df['entity_name'] = level_df[name_col]

        # On sélectionne les colonnes utiles
        final_cols = ['level', 'entity_id', 'entity_name'] + \
                     [col for col in level_df.columns if col.startswith('prop_')] + \
                     [col for col in level_df.columns if col.startswith('count_')] + \
                     ['proportion_fixed_on_time', 'vuln_count', 'vuln_total']

        all_results.append(level_df[final_cols])
    # Concaténation finale
    return pd.concat(all_results, ignore_index=True)


def on_time_correction_rate(pc_df):
    pc_df = pc_df[pc_df["severity"] != 1].copy()

    # Masques logiques
    pc_df['considered'] = pc_df['correction'] | (
        (~pc_df['correction']) & (pc_df['correction_delay'] > pc_df['patching_cadence_days'])
    )

    pc_df['on_time_fix'] = pc_df['correction'] & (pc_df['correction_delay'] <= pc_df['patching_cadence_days'])

    pd.set_option('display.max_columns', None)
    res = concat_all_levels(pc_df)
    res['security_score'] = res.apply(compute_security_score, axis=1)
    res.to_csv('res.csv', sep=";")

severity_weights = {
    'low': 1,
    'medium': 2,
    'high': 4,
    'critical': 6
}


def compute_security_score(row):
    weighted_sum = 0
    total_weight = 0
    for sev, weight in severity_weights.items():
        col = f'prop_{sev}_on_time'
        if pd.notnull(row.get(col)):
            weighted_sum += weight * row[col]
            total_weight += weight
    return round((weighted_sum / total_weight), 1) if total_weight > 0 else np.nan

if __name__ == "__main__":
    default_gain = [0, 1, 3, 5]
    default_lose = [15, 135, 45, 30]
    default_factor = [0, 4, 24, 73]
    pc, _ = load_and_prepare_data()
    # new_pc = add_penalty(pc, default_gain, default_lose, default_factor)
    # new_pc.to_csv("pc_new_with_pen.csv")
    on_time_correction_rate(pc)
    
